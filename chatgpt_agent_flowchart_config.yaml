# ChatGPT Agent 流程图配置文件
# 将原780行Prompt重构为结构化配置

system:
  name: "ChatGPT Agent Flowchart System"
  version: "2.0"
  description: "基于流程图的模块化AI编程系统"

# 主流程控制
main_flow:
  entry_point: "user_input"
  
  # 核心决策节点
  decision_nodes:
    task_preprocessing:
      type: "condition"
      condition: "input_validation"
      branches:
        - condition: "contains_sensitive_content"
          action: "return_error"
        - condition: "valid_input"
          action: "proceed_to_analysis"
    
    task_type_identification:
      type: "multi_branch"
      branches:
        - type: "research_task"
          module: "research_agent"
        - type: "coding_task"
          module: "code_agent"
        - type: "tool_operation"
          module: "tool_agent"
        - type: "integration_task"
          module: "integration_agent"
        - type: "complex_task"
          module: "multi_agent_collaboration"
    
    quality_check:
      type: "condition"
      condition: "quality_validation"
      branches:
        - condition: "quality_passed"
          action: "proceed_to_output"
        - condition: "quality_failed"
          action: "error_recovery_flow"

# 模块定义
modules:
  research_agent:
    workflow:
      - step: "requirement_analysis"
        description: "分析研究需求"
      - step: "information_collection"
        description: "多渠道信息搜集"
      - step: "content_analysis"
        description: "提取关键信息"
      - step: "result_integration"
        description: "整合研究结果"
    
    tools:
      - "search_engine"
      - "documentation_query"
      - "code_repository_search"
    
    output_format:
      summary: "研究摘要"
      key_findings: "关键发现"
      recommendations: "建议方案"
      references: "参考资料"

  code_agent:
    workflow:
      - step: "requirement_conversion"
        description: "需求转技术实现"
      - step: "code_generation"
        description: "生成高质量代码"
      - step: "execution_validation"
        description: "执行验证测试"
      - step: "optimization"
        description: "性能优化改进"
    
    quality_checks:
      - type: "syntax_validation"
        retry_on_fail: true
        max_retries: 3
      - type: "logic_validation"
        retry_on_fail: true
        max_retries: 2
      - type: "performance_check"
        retry_on_fail: false
    
    tools:
      - "code_editor"
      - "compiler"
      - "debugger"
      - "test_framework"

  tool_agent:
    workflow:
      - step: "tool_identification"
        description: "识别合适工具"
      - step: "capability_matching"
        description: "能力匹配分析"
      - step: "operation_execution"
        description: "执行工具操作"
      - step: "result_validation"
        description: "验证操作结果"
    
    fallback_strategy:
      - primary_tool_fails: "switch_to_backup_tool"
      - all_tools_fail: "manual_guidance"
    
    supported_tools:
      cursor:
        capabilities: ["code_generation", "completion", "chat"]
        commands:
          generate: "⌘+K {requirement}"
          complete: "Tab"
          chat: "⌘+L"
      
      github_copilot:
        capabilities: ["code_suggestion", "chat", "review"]
        commands:
          generate: "// {requirement}"
          chat: "Copilot Chat"
      
      cline:
        capabilities: ["multi_model", "automation", "vscode_integration"]
        models: ["claude-3.5-sonnet", "gpt-4", "gemini-pro"]

  integration_agent:
    workflow:
      - step: "data_consolidation"
        description: "整合多源数据"
      - step: "state_synchronization"
        description: "同步系统状态"
      - step: "quality_assurance"
        description: "质量保证检查"
      - step: "delivery_packaging"
        description: "打包交付物"

# 错误处理流程
error_handling:
  error_types:
    syntax_error:
      detection: "code_syntax_check"
      recovery: "auto_fix_syntax"
      fallback: "regenerate_code"
      max_retries: 3
    
    runtime_error:
      detection: "execution_monitoring"
      recovery: "exception_handling"
      fallback: "degraded_execution"
      max_retries: 2
    
    tool_error:
      detection: "tool_call_failure"
      recovery: "switch_backup_tool"
      fallback: "manual_operation"
      max_retries: 1
    
    logic_error:
      detection: "result_validation"
      recovery: "logic_correction"
      fallback: "requirement_reanalysis"
      max_retries: 2

# 循环控制
loops:
  quality_improvement_loop:
    condition: "quality_check_failed"
    actions:
      - "collect_feedback"
      - "analyze_issues"
      - "apply_corrections"
      - "re_execute"
    max_iterations: 3
  
  tool_retry_loop:
    condition: "tool_operation_failed"
    actions:
      - "identify_failure_cause"
      - "select_alternative_tool"
      - "retry_operation"
    max_iterations: 2

# 决策权重算法
decision_weights:
  task_complexity: 0.3
  tool_efficiency: 0.25
  success_probability: 0.2
  resource_cost: 0.15
  user_preference: 0.1

# 输出配置
output:
  format: "structured"
  include_reasoning: true
  quality_metrics: true
  execution_log: true

# 调试模式
debug:
  enabled: false
  log_level: "info"
  trace_execution: false
  include_internal_state: false
