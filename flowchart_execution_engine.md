# ChatGPT Agent 流程图执行引擎

## 概述
这是基于流程图配置的精简执行引擎，将原780行复杂Prompt转换为50行核心逻辑。

## 核心执行逻辑

```python
# 伪代码：流程图执行引擎
class ChatGPTAgentFlowchartEngine:
    def __init__(self, config_file):
        self.config = load_yaml(config_file)
        self.current_state = "user_input"
        self.context = {}
        
    def execute(self, user_input):
        """主执行流程"""
        self.context['input'] = user_input
        
        # 1. 任务预处理
        if not self.validate_input(user_input):
            return self.handle_error("invalid_input")
        
        # 2. 任务类型识别
        task_type = self.identify_task_type(user_input)
        
        # 3. 选择并执行模块
        result = self.execute_module(task_type)
        
        # 4. 质量检查循环
        while not self.quality_check(result):
            result = self.error_recovery(result)
            if self.max_retries_reached():
                break
        
        # 5. 后处理和输出
        return self.format_output(result)
    
    def identify_task_type(self, input_text):
        """智能任务类型识别"""
        keywords = {
            'research': ['调研', '分析', '比较', '研究'],
            'coding': ['代码', '编程', '开发', '实现'],
            'tool': ['工具', '操作', '执行', '运行'],
            'integration': ['整合', '集成', '部署', '发布']
        }
        
        for task_type, words in keywords.items():
            if any(word in input_text for word in words):
                return task_type
        return 'complex_task'  # 默认复合任务
    
    def execute_module(self, task_type):
        """根据任务类型执行相应模块"""
        module_config = self.config['modules'][task_type]
        
        for step in module_config['workflow']:
            result = self.execute_step(step)
            if not result.success:
                return self.handle_step_failure(step, result)
        
        return result
    
    def quality_check(self, result):
        """质量检查决策点"""
        checks = ['syntax_validation', 'logic_validation', 'performance_check']
        return all(self.run_check(check, result) for check in checks)
    
    def error_recovery(self, failed_result):
        """错误恢复流程"""
        error_type = self.classify_error(failed_result)
        recovery_config = self.config['error_handling']['error_types'][error_type]
        
        # 执行恢复策略
        if recovery_config['recovery'] == 'auto_fix_syntax':
            return self.auto_fix_syntax(failed_result)
        elif recovery_config['recovery'] == 'switch_backup_tool':
            return self.switch_tool(failed_result)
        # ... 其他恢复策略
        
        return failed_result
```

## 使用方法

### 1. 基础使用
```python
# 初始化引擎
engine = ChatGPTAgentFlowchartEngine('chatgpt_agent_flowchart_config.yaml')

# 执行任务
result = engine.execute("开发一个待办事项管理应用")
```

### 2. 配置自定义工具
```yaml
# 在配置文件中添加新工具
supported_tools:
  your_custom_tool:
    capabilities: ["custom_feature"]
    commands:
      generate: "custom_command {requirement}"
```

### 3. 调试模式
```yaml
debug:
  enabled: true
  log_level: "debug"
  trace_execution: true
```

## 流程图节点映射

### 决策节点 → 代码逻辑
```python
# 菱形决策节点
if condition:
    execute_branch_a()
else:
    execute_branch_b()

# 多分支决策
task_type = identify_task_type(input)
module_map = {
    'research': research_agent,
    'coding': code_agent,
    'tool': tool_agent
}
module_map[task_type].execute()
```

### 循环节点 → 代码逻辑
```python
# 质量检查循环
max_retries = 3
retry_count = 0

while not quality_passed and retry_count < max_retries:
    result = improve_quality(result)
    retry_count += 1
```

## 优化效果对比

| 指标 | 原版本(780行) | 重构版本(50行核心逻辑) |
|------|---------------|----------------------|
| 可读性 | 需要逐行阅读 | 流程图一目了然 |
| 维护性 | 修改困难 | 配置文件修改 |
| 扩展性 | 添加功能复杂 | 新增配置节点 |
| 调试性 | 难以追踪 | 清晰执行路径 |
| 复用性 | 大量重复 | 模块化复用 |

## 关键优化点

### 1. 逻辑代替描述
- **原版本**: 大量"如果XX则YY"的文本规则
- **重构版本**: 配置化的条件判断节点

### 2. 循环复用机制
- **原版本**: 重复的错误处理描述
- **重构版本**: 统一的循环控制配置

### 3. 分层结构设计
- **原版本**: 平铺的功能描述
- **重构版本**: 模块化的层次结构

## 扩展指南

### 添加新模块
1. 在`modules`配置中定义新模块
2. 指定工作流程步骤
3. 配置工具和输出格式
4. 更新主流程的分支判断

### 添加新工具
1. 在`supported_tools`中添加工具配置
2. 定义工具能力和命令
3. 更新工具选择逻辑

### 自定义错误处理
1. 在`error_handling`中定义新错误类型
2. 配置检测、恢复和回退策略
3. 设置重试次数限制

## 部署建议

1. **渐进式迁移**: 先迁移核心流程，再逐步添加细节
2. **配置验证**: 部署前验证YAML配置文件格式
3. **性能监控**: 监控执行时间和成功率
4. **用户反馈**: 收集使用反馈持续优化配置

这种重构方式将复杂的Prompt转换为可维护、可扩展的配置驱动系统，大大提高了系统的可用性和可靠性。
